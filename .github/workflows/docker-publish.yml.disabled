name: Zep Server Docker Build and Publish

on:
  push:
   # Publish semver tags as releases.
   tags: [ 'v*.*.*' ]

env:
  REGISTRY: docker.io
  IMAGE_NAME: zepai/zep

jobs:
  docker-image:
    environment:
      name: release
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.inputs.tag || github.ref  }}

      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v4.4.0
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=match,pattern=v(.*-beta),group=1
            type=match,pattern=v.*-(beta),group=1

      - name: Depot build and push image
        uses: depot/build-push-action@v1
        with:
          project: v9jv1mlpwc
          context: .
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags || env.TAGS }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          file: Dockerfile.ce
