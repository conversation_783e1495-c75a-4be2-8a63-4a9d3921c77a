name: golangci-lint
on:
  push:
    tags:
      - v*
  pull_request:
    branches:
      - main
permissions:
  contents: read
jobs:
  golangci:
    name: lint
    runs-on: depot-ubuntu-22.04-8
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: '1.22'
          cache: false
      - uses: actions/checkout@v4
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          working-directory: ./src
          version: v1.61.0
          args:
            --config=golangci.yaml
