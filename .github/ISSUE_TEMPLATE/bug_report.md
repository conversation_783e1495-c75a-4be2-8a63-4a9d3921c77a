---
name: Bug report
about: Create a report to help us improve
title: "[BUG]"
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Please provide a clear set of steps that would allow us to reproduce the bug. Provide code samples or shell commands as necessary.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Logs**
Please provide your Zep server and applications logs. 

**Environment (please complete the following information):**
 - Zep version: [e.g. vX.X.X]
 - Zep SDK and version: [e.g. `zep-js` or `zep-python` and vX.X.X]
 - Deployment [e.g. using `docker compose`, to a hosted environment such as Render]

_Note_: The Zep server version is available in the Zep server logs at startup:
`Starting zep server version 0.11.0-cbf4fe4 (2023-08-30T12:49:03+0000)`

**Additional context**
Add any other context about the problem here.
