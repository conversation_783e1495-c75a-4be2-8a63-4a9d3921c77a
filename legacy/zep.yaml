log:
  # debug, info, warn, error, panic, dpanic, or fatal. Default = info
  level: info
  # How should logs be formatted? Setting to "console" will print human readable logs
  # whie "json" will print structured JSON logs. Default is "json".
  format: json
http:
  # Host to bind to. Default is 0.0.0.0
  host: 0.0.0.0
  # Port to bind to. Default is 8000
  port: 8000
  max_request_size: 5242880
postgres:
  user: postgres
  password: postgres
  host: db
  port: 5432
  database: postgres
  schema_name: public
  read_timeout: 30
  write_timeout: 30
  max_open_connections: 10
# Carbon is a package used for dealing with time - github.com/golang-module/carbon
# It is primarily used for generating humand readable relative time strings like "2 hours ago".
# See the list of supported languages here https://github.com/golang-module/carbon?tab=readme-ov-file#i18n
carbon:
  locale: en
graphiti:
   # Base url to the graphiti service
  service_url: http://graphiti:8003
# In order to authenicate API requests to the Zep service, a secret must be provided.
# This secret should be kept secret between the Zep service and the client. It can be any string value.
# When making requests to the Zep service, include the secret in the Authorization header.
api_secret:
# In order to better understand how Zep is used, we can collect telemetry data.
# This is optional and can be disabled by setting disabled to true.
# We do not collect any PII or any of your data. We only collect anonymized data
# about how Zep is used.
telemetry:
  disabled: false
  # Please provide an identifying name for your organization so can get a better understanding
  # about who is using Zep. This is optional.
  organization_name:
