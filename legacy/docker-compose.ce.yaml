name: zep-ce

services:
  zep:
    image: zepai/zep:latest
    #    build:
    #      context: .
    #      dockerfile: Dockerfile.ce
    ports:
      - "8000:8000"
    volumes:
      - ./zep.yaml:/app/zep.yaml
    environment:
      - ZEP_CONFIG_FILE=zep.yaml
    networks:
      - zep-network
    depends_on:
      graphiti:
        condition: service_healthy
      db:
        condition: service_healthy
  db:
    image: ankane/pgvector:v0.5.1
    container_name: zep-ce-postgres
    restart: on-failure
    shm_size: "128mb" # Increase this if vacuuming fails with a "no space left on device" error
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    networks:
      - zep-network
    healthcheck:
      test: ["CMD", "pg_isready", "-q", "-d", "postgres", "-U", "postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - zep-db:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  graphiti:
    image: zepai/graphiti:0.3
    ports:
      - "8003:8003"
    env_file:
      - .env
    networks:
      - zep-network
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:8003/healthcheck')",
        ]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_NAME=gpt-4o-mini
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=zepzepzep
      - PORT=8003
  neo4j:
    image: neo4j:5.22.0
    networks:
      - zep-network
    healthcheck:
      test: wget http://localhost:7687 || exit 1
      interval: 1s
      timeout: 10s
      retries: 20
      start_period: 3s
    ports:
      - "7474:7474" # HTTP
      - "7687:7687" # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=neo4j/zepzepzep
volumes:
  neo4j_data:
  zep-db:
networks:
  zep-network:
    driver: bridge