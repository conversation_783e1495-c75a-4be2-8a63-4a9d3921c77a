# Dynamic Relationship Extraction System for Zep Knowledge Graphs

A comprehensive system for extracting entities and relationships from conversational text without predefined schemas, building flexible ontologies, and using semantic inference to identify implicit relationships across any domain.

## Overview

This system provides a complete solution for dynamic knowledge graph construction that can:

- **Extract entities and relationships dynamically** from any conversational text without predefined schemas
- **Build flexible ontologies** that adapt to discovered patterns in conversations
- **Use semantic inference** to identify implicit relationships beyond explicit mentions
- **Work across different domains** (travel, shopping, healthcare, etc.) without domain-specific configuration
- **Integrate seamlessly with Zep's knowledge graph system**

## Architecture

The system consists of four main components:

### 1. DynamicRelationshipExtractor
- Uses LLM prompting (OpenAI GPT-4) to extract entities and relationships from conversational text
- Identifies temporal context (when, how often, duration)
- Captures emotional/preference context (likes, dislikes, changes over time)
- Generates confidence scores for extracted relationships

### 2. GenericOntologyBuilder
- Creates flexible entity and relationship models based on discovered patterns
- Builds dynamic `EntityModel` and `EdgeModel` classes for Zep integration
- Adapts to any conversational domain without predefined schemas
- Analyzes patterns to optimize ontology structure

### 3. SemanticRelationshipInferencer
- Uses spaCy dependency parsing to find implicit relationships
- Analyzes co-occurrence patterns, temporal relationships, and causal connections
- Identifies relationships not explicitly stated in the conversation
- Provides additional semantic context through NLP techniques

### 4. DomainAgnosticKnowledgeGraph
- Orchestrates the entire extraction and integration process
- Coordinates all components to process conversations end-to-end
- Integrates with Zep's graph ontology system
- Provides insights and analytics across processed conversations

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Download the spaCy English model:
```bash
python -m spacy download en_core_web_sm
```

3. Set up your environment variables:
```bash
export ZEP_API_KEY="your_zep_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

## Quick Start

```python
from domain_agnostic_knowledge_graph import DomainAgnosticKnowledgeGraph
from zep_cloud.types import Message

# Initialize the system
kg_system = DomainAgnosticKnowledgeGraph(
    zep_api_key="your_zep_api_key",
    openai_api_key="your_openai_api_key"
)

# Create conversation messages
messages = [
    Message(role="user", role_type="user", content="I love Italian food, especially pasta dishes."),
    Message(role="assistant", role_type="assistant", content="What's your favorite type of pasta?"),
    Message(role="user", role_type="user", content="I really enjoy carbonara, but I've been trying to eat healthier lately.")
]

# Process the conversation
result = kg_system.process_conversation(
    messages=messages,
    context="Food preferences conversation"
)

# View results
print(f"Extracted {len(result.entities)} entities and {len(result.relationships)} relationships")
print(f"Created {len(result.ontology_entities)} entity types and {len(result.ontology_edges)} relationship types")

# Integrate with Zep
success = kg_system.integrate_with_zep(result, user_id="user123", session_id="session456")
```

## Domain Examples

The system includes comprehensive examples for three different domains:

### Travel Domain
```python
python dynamic_kg_examples.py
```

**Sample Conversation:**
- User planning a trip to Japan with spouse
- Discusses food preferences, photography interests, accommodation needs
- Mentions past experiences and future plans

**Extracted Entities:**
- Person: Sarah (spouse, photographer)
- Destination: Japan, Tokyo, Osaka, Kyoto
- Experience: Kaiseki dining, cooking classes
- Accommodation: Boutique hotels, ryokan style

**Extracted Relationships:**
- User TRAVELS_WITH Sarah
- Sarah INTERESTED_IN photography
- User PREFERS traditional architecture
- User VISITED Kyoto (temporal: 3 years ago)

### Shopping Domain
**Sample Conversation:**
- User looking for graphic design equipment
- Discusses current setup, performance issues, budget constraints
- Mentions brand preferences and past experiences

**Extracted Entities:**
- Product: MacBook Pro, LG Monitor, Synology NAS
- Brand: Apple, LG, Dell
- Specification: M3 Max chip, 27-inch display
- Budget: $3000-4000

**Extracted Relationships:**
- User USES MacBook Pro (temporal: since 2019)
- User PREFERS Mac workflow
- User HAD_GOOD_EXPERIENCE_WITH LG (temporal: 6 years)
- User NEEDS better performance

### Healthcare Domain
**Sample Conversation:**
- User discussing sleep issues with healthcare provider
- Mentions job change, stress factors, current habits
- Explores potential solutions and lifestyle changes

**Extracted Entities:**
- Symptom: Insomnia, racing thoughts
- Cause: Job stress, career transition
- Habit: Coffee consumption, phone usage
- Solution: Counseling, meditation, caffeine reduction

**Extracted Relationships:**
- Job change CAUSES sleep issues (temporal: 3 weeks ago)
- User TRANSITIONED_FROM developer TO project manager
- Caffeine INTERFERES_WITH sleep
- User CONSIDERS counseling services

## Key Features

### Temporal Context Extraction
- Identifies when relationships apply ("3 years ago", "since starting new job")
- Tracks frequency patterns ("daily", "usually", "sometimes")
- Captures duration information ("for hours", "6-8 hours")

### Emotional and Preference Context
- Detects sentiment and preferences ("loves", "dislikes", "prefers")
- Tracks changes over time ("used to", "now prefers", "lately")
- Identifies emotional states and their triggers

### Confidence Scoring
- **High Confidence**: Explicitly stated relationships with clear context
- **Medium Confidence**: Implied relationships with good supporting evidence
- **Low Confidence**: Inferred relationships based on patterns or co-occurrence

### Pattern Matching
- Recognizes common relationship types across domains
- Adapts to domain-specific vocabulary and concepts
- Builds reusable patterns for similar conversations

## Integration with Zep

The system seamlessly integrates with Zep's knowledge graph capabilities:

1. **Dynamic Ontology Creation**: Automatically creates `EntityModel` and `EdgeModel` classes
2. **Graph Population**: Populates the knowledge graph with extracted entities and relationships
3. **Temporal Tracking**: Leverages Zep's temporal capabilities for time-based relationships
4. **User Context**: Maintains user-specific knowledge graphs across sessions

## Advanced Usage

### Custom Processing
```python
# Process with specific context
result = kg_system.process_conversation(
    messages=messages,
    context="Healthcare consultation about sleep disorders",
    user_id="patient123",
    session_id="consultation456"
)

# Access detailed results
for entity in result.entities:
    print(f"Entity: {entity.name} ({entity.entity_type})")
    print(f"Confidence: {entity.confidence.value}")
    print(f"Attributes: {entity.attributes}")
    print(f"Temporal Context: {entity.temporal_context}")

for relationship in result.relationships:
    print(f"Relationship: {relationship.source_entity} --[{relationship.relationship_type}]--> {relationship.target_entity}")
    print(f"Confidence: {relationship.confidence.value}")
    print(f"Emotional Context: {relationship.emotional_context}")
```

### Analytics and Insights
```python
# Get insights across multiple conversations
insights = kg_system.get_domain_insights()
print(f"Most common entity types: {insights['most_common_entity_types']}")
print(f"Most common relationships: {insights['most_common_relationship_types']}")
print(f"Average confidence: {insights['confidence_distribution']}")
```

### Custom Configuration
```python
# Initialize with custom settings
kg_system = DomainAgnosticKnowledgeGraph(
    zep_api_key="your_key",
    openai_api_key="your_key",
    spacy_model="en_core_web_lg"  # Use larger spaCy model for better accuracy
)

# Access individual components
extractor = kg_system.extractor
ontology_builder = kg_system.ontology_builder
semantic_inferencer = kg_system.semantic_inferencer
```

## Performance Considerations

- **LLM Calls**: Each conversation requires 1 OpenAI API call for entity/relationship extraction
- **spaCy Processing**: Local NLP processing for semantic inference
- **Memory Usage**: Scales with conversation length and complexity
- **Zep Integration**: Efficient batch operations for graph updates

## Troubleshooting

### Common Issues

1. **Missing API Keys**: Ensure `ZEP_API_KEY` and `OPENAI_API_KEY` are set
2. **spaCy Model**: Install the English model with `python -m spacy download en_core_web_sm`
3. **Rate Limits**: OpenAI API rate limits may affect processing speed
4. **Memory**: Large conversations may require more memory for processing

### Error Handling

The system includes comprehensive error handling:
- Graceful degradation when components fail
- Logging for debugging and monitoring
- Fallback mechanisms for missing dependencies

## Contributing

This system is designed to be extensible. You can:

- Add new semantic inference techniques
- Implement domain-specific extractors
- Enhance the ontology building algorithms
- Improve confidence scoring mechanisms

## License

This example is part of the Zep project and follows the same licensing terms.
