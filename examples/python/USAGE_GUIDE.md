# Dynamic Relationship Extraction System - Usage Guide

This guide provides step-by-step instructions for using the Dynamic Relationship Extraction System with Zep's knowledge graph capabilities.

## Quick Setup

### 1. Install Dependencies
```bash
# Run the setup script
python setup_dynamic_kg.py

# Or install manually
pip install -r requirements.txt
python -m spacy download en_core_web_sm
```

### 2. Set Environment Variables
```bash
export ZEP_API_KEY="your_zep_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

### 3. Run Tests
```bash
python test_dynamic_kg.py
```

### 4. Try Examples
```bash
python dynamic_kg_examples.py
```

## Basic Usage

### Simple Conversation Processing

```python
from domain_agnostic_knowledge_graph import DomainAgnosticKnowledgeGraph
from zep_cloud.types import Message

# Initialize system
kg_system = DomainAgnosticKnowledgeGraph(
    zep_api_key="your_zep_key",
    openai_api_key="your_openai_key"
)

# Create conversation
messages = [
    Message(role="user", role_type="user", 
           content="I've been going to the gym regularly for 2 years. I love weightlifting but hate cardio."),
    Message(role="assistant", role_type="assistant", 
           content="That's great consistency! What's your favorite weightlifting exercise?"),
    Message(role="user", role_type="user", 
           content="Deadlifts are my favorite. I can lift 300 pounds now, up from 200 when I started.")
]

# Process conversation
result = kg_system.process_conversation(messages, context="Fitness conversation")

# View results
print(f"Entities: {len(result.entities)}")
print(f"Relationships: {len(result.relationships)}")

# Integrate with Zep
kg_system.integrate_with_zep(result, user_id="fitness_user_123")
```

## Advanced Features

### 1. Temporal Context Extraction

The system automatically extracts temporal information:

```python
# Example conversation with temporal elements
messages = [
    Message(role="user", role_type="user", 
           content="I used to hate vegetables as a child, but now I eat them daily. I started this healthy diet 6 months ago after my doctor's recommendation.")
]

result = kg_system.process_conversation(messages)

# Check temporal contexts
for relationship in result.relationships:
    if relationship.temporal_context:
        print(f"{relationship.source_entity} --[{relationship.relationship_type}]--> {relationship.target_entity}")
        print(f"  Temporal: {relationship.temporal_context}")
```

### 2. Emotional and Preference Context

```python
# Example with emotional context
messages = [
    Message(role="user", role_type="user", 
           content="I absolutely love Italian cuisine! Pizza is my favorite, but I'm trying to avoid it lately because I want to lose weight.")
]

result = kg_system.process_conversation(messages)

# Check emotional contexts
for relationship in result.relationships:
    if relationship.emotional_context:
        print(f"Emotional context: {relationship.emotional_context}")
```

### 3. Confidence Scoring

```python
result = kg_system.process_conversation(messages)

# View confidence statistics
stats = result.confidence_stats
print(f"Overall confidence: {stats['overall_confidence']:.2f}")
print(f"High confidence entities: {stats['entity_confidence']['high']}")
print(f"High confidence relationships: {stats['relationship_confidence']['high']}")

# Filter by confidence level
high_confidence_entities = [e for e in result.entities if e.confidence.value == "high"]
high_confidence_relationships = [r for r in result.relationships if r.confidence.value == "high"]
```

### 4. Domain-Specific Processing

```python
# Travel domain
travel_result = kg_system.process_conversation(
    travel_messages, 
    context="Travel planning conversation"
)

# Healthcare domain
healthcare_result = kg_system.process_conversation(
    healthcare_messages, 
    context="Healthcare consultation about sleep disorders"
)

# Shopping domain
shopping_result = kg_system.process_conversation(
    shopping_messages, 
    context="Technology shopping for professional equipment"
)
```

### 5. Cross-Domain Analysis

```python
# Process multiple domains
results = [travel_result, healthcare_result, shopping_result]

# Get insights across domains
insights = kg_system.get_domain_insights(results)

print(f"Total conversations: {insights['total_conversations_processed']}")
print(f"Most common entity types: {insights['most_common_entity_types'][:5]}")
print(f"Most common relationships: {insights['most_common_relationship_types'][:5]}")
```

## Working with Individual Components

### 1. Direct Entity Extraction

```python
from dynamic_relationship_extractor import DynamicRelationshipExtractor

extractor = DynamicRelationshipExtractor("your_openai_key")
entities, relationships = extractor.extract_entities_and_relationships(
    "I love sushi and visit Tokyo Sushi Bar every Friday with my wife Sarah."
)

for entity in entities:
    print(f"Entity: {entity.name} ({entity.entity_type})")
    print(f"  Attributes: {entity.attributes}")
    print(f"  Confidence: {entity.confidence.value}")
```

### 2. Semantic Inference

```python
from dynamic_relationship_extractor import SemanticRelationshipInferencer

inferencer = SemanticRelationshipInferencer()
implicit_rels = inferencer.infer_implicit_relationships(
    "John works at Google. Sarah is his wife. They live in San Francisco.",
    entities  # Previously extracted entities
)

for rel in implicit_rels:
    print(f"Implicit: {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity}")
```

### 3. Dynamic Ontology Building

```python
from dynamic_relationship_extractor import GenericOntologyBuilder

builder = GenericOntologyBuilder()

# Analyze patterns
patterns = builder.analyze_patterns(entities, relationships)

# Create dynamic models
entity_models = builder.create_dynamic_entity_models(patterns)
edge_models = builder.create_dynamic_edge_models(patterns)

print(f"Created {len(entity_models)} entity types")
print(f"Created {len(edge_models)} relationship types")
```

## Integration Patterns

### 1. Session-Based Processing

```python
# Process multiple sessions for the same user
user_id = "user_123"
sessions = [
    ("session_1", travel_messages),
    ("session_2", shopping_messages),
    ("session_3", healthcare_messages)
]

for session_id, messages in sessions:
    result = kg_system.process_conversation(messages)
    kg_system.integrate_with_zep(result, user_id, session_id)
```

### 2. Batch Processing

```python
# Process multiple conversations in batch
conversations = [
    (user1_messages, "user_1"),
    (user2_messages, "user_2"),
    (user3_messages, "user_3")
]

results = []
for messages, user_id in conversations:
    result = kg_system.process_conversation(messages)
    kg_system.integrate_with_zep(result, user_id)
    results.append(result)

# Analyze batch results
batch_insights = kg_system.get_domain_insights(results)
```

### 3. Real-time Processing

```python
# Process messages as they arrive
def process_new_message(user_id, session_id, new_message):
    # Get existing conversation context
    existing_messages = get_session_messages(session_id)  # Your implementation
    all_messages = existing_messages + [new_message]
    
    # Process updated conversation
    result = kg_system.process_conversation(all_messages)
    
    # Update Zep knowledge graph
    success = kg_system.integrate_with_zep(result, user_id, session_id)
    
    return result, success
```

## Error Handling and Debugging

### 1. Handling API Errors

```python
try:
    result = kg_system.process_conversation(messages)
except Exception as e:
    print(f"Processing error: {e}")
    # Fallback to basic processing or cached results
```

### 2. Debugging Extraction Results

```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

result = kg_system.process_conversation(messages)

# Examine extraction details
for entity in result.entities:
    print(f"Entity: {entity.name}")
    print(f"  Source text: '{entity.source_text}'")
    print(f"  Confidence: {entity.confidence.value}")
    print(f"  Attributes: {entity.attributes}")

for rel in result.relationships:
    print(f"Relationship: {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity}")
    print(f"  Source text: '{rel.source_text}'")
    print(f"  Confidence: {rel.confidence.value}")
```

### 3. Validating Results

```python
# Check result quality
def validate_result(result):
    issues = []
    
    # Check for empty results
    if not result.entities and not result.relationships:
        issues.append("No entities or relationships extracted")
    
    # Check confidence distribution
    low_confidence_count = sum(1 for e in result.entities if e.confidence.value == "low")
    if low_confidence_count > len(result.entities) * 0.5:
        issues.append("Too many low-confidence entities")
    
    # Check for reasonable entity types
    entity_types = set(e.entity_type for e in result.entities)
    if "Unknown" in entity_types and len(entity_types) == 1:
        issues.append("All entities have unknown type")
    
    return issues

issues = validate_result(result)
if issues:
    print("Quality issues found:")
    for issue in issues:
        print(f"  - {issue}")
```

## Performance Optimization

### 1. Caching Results

```python
import pickle
from pathlib import Path

# Cache processing results
def cache_result(result, cache_key):
    cache_dir = Path("cache")
    cache_dir.mkdir(exist_ok=True)
    
    with open(cache_dir / f"{cache_key}.pkl", "wb") as f:
        pickle.dump(result, f)

def load_cached_result(cache_key):
    cache_file = Path("cache") / f"{cache_key}.pkl"
    if cache_file.exists():
        with open(cache_file, "rb") as f:
            return pickle.load(f)
    return None
```

### 2. Batch API Calls

```python
# Process multiple short conversations together
def batch_process_conversations(conversation_batches):
    results = []
    
    for batch in conversation_batches:
        # Combine short conversations for more efficient LLM processing
        combined_text = "\n---\n".join([
            " ".join(msg.content for msg in messages) 
            for messages in batch
        ])
        
        # Process as single request
        entities, relationships = kg_system.extractor.extract_entities_and_relationships(combined_text)
        
        # Split results back to individual conversations
        # (Implementation depends on your specific needs)
        
    return results
```

## Best Practices

1. **Context Matters**: Always provide meaningful context for better extraction quality
2. **Validate Results**: Check confidence scores and validate critical extractions
3. **Handle Errors**: Implement proper error handling for API failures
4. **Monitor Performance**: Track processing times and API usage
5. **Cache When Possible**: Cache results for repeated processing of the same content
6. **Test Thoroughly**: Use the provided test suite and add your own domain-specific tests

## Troubleshooting

### Common Issues

1. **Low Extraction Quality**: Try providing more specific context or using longer conversations
2. **API Rate Limits**: Implement retry logic with exponential backoff
3. **Memory Issues**: Process large conversations in chunks
4. **spaCy Model Issues**: Ensure the English model is properly installed

### Getting Help

- Check the test suite: `python test_dynamic_kg.py`
- Review the examples: `python dynamic_kg_examples.py`
- Enable debug logging for detailed information
- Consult the main README: `README_dynamic_kg.md`
