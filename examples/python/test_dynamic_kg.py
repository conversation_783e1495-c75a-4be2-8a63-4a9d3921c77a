#!/usr/bin/env python3
"""
Test script for the Dynamic Relationship Extraction System

This script runs comprehensive tests to validate the system functionality.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch
from typing import List

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dynamic_relationship_extractor import (
    DynamicRelationshipExtractor,
    GenericOntologyBuilder,
    SemanticRelationshipInferencer,
    ExtractedEntity,
    ExtractedRelationship,
    ConfidenceLevel
)
from domain_agnostic_knowledge_graph import DomainAgnosticKnowledgeGraph, ProcessingResult
from zep_cloud.types import Message


class TestDynamicRelationshipExtractor(unittest.TestCase):
    """Test the DynamicRelationshipExtractor class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.extractor = DynamicRelationshipExtractor("fake_api_key")
    
    def test_initialization(self):
        """Test extractor initialization"""
        self.assertIsNotNone(self.extractor)
        self.assertEqual(self.extractor.model, "gpt-4")
        self.assertIn("preference", self.extractor.relationship_patterns)
    
    def test_temporal_pattern_extraction(self):
        """Test temporal pattern extraction"""
        text = "I visit the gym daily and have been going there for 3 years since I moved here."
        patterns = self.extractor.extract_temporal_patterns(text)
        
        self.assertIn("frequency", patterns)
        self.assertIn("daily", patterns["frequency"])
        self.assertIn("duration", patterns)
        self.assertIn("3 years", patterns["duration"])
        self.assertIn("time_references", patterns)
        self.assertIn("since", patterns["time_references"])
    
    def test_emotional_context_extraction(self):
        """Test emotional context extraction"""
        text = "I love pizza but hate broccoli. The restaurant was amazing!"
        context = self.extractor.extract_emotional_context(text)
        
        self.assertIn("positive_sentiment", context)
        self.assertIn("love", context["positive_sentiment"])
        self.assertIn("amazing", context["positive_sentiment"])
        self.assertIn("negative_sentiment", context)
        self.assertIn("hate", context["negative_sentiment"])
        self.assertEqual(context["overall_sentiment"], "positive")
    
    @patch('openai.OpenAI')
    def test_extraction_with_mock_llm(self, mock_openai):
        """Test entity and relationship extraction with mocked LLM"""
        # Mock the OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '''
        {
            "entities": [
                {
                    "name": "John",
                    "type": "Person",
                    "attributes": {"age": "30"},
                    "confidence": "high",
                    "temporal_context": "current",
                    "source_text": "John is 30"
                }
            ],
            "relationships": [
                {
                    "source": "John",
                    "target": "Pizza",
                    "type": "LIKES",
                    "attributes": {"intensity": "high"},
                    "confidence": "high",
                    "temporal_context": "current",
                    "emotional_context": "positive",
                    "source_text": "John likes pizza"
                }
            ]
        }
        '''
        
        mock_openai.return_value.chat.completions.create.return_value = mock_response
        
        entities, relationships = self.extractor.extract_entities_and_relationships(
            "John is 30 and likes pizza"
        )
        
        self.assertEqual(len(entities), 1)
        self.assertEqual(entities[0].name, "John")
        self.assertEqual(entities[0].entity_type, "Person")
        self.assertEqual(entities[0].confidence, ConfidenceLevel.HIGH)
        
        self.assertEqual(len(relationships), 1)
        self.assertEqual(relationships[0].source_entity, "John")
        self.assertEqual(relationships[0].target_entity, "Pizza")
        self.assertEqual(relationships[0].relationship_type, "LIKES")


class TestGenericOntologyBuilder(unittest.TestCase):
    """Test the GenericOntologyBuilder class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.builder = GenericOntologyBuilder()
        
        # Create sample entities and relationships
        self.sample_entities = [
            ExtractedEntity("John", "Person", {"age": 30}, ConfidenceLevel.HIGH),
            ExtractedEntity("Pizza", "Food", {"type": "Italian"}, ConfidenceLevel.MEDIUM),
            ExtractedEntity("Restaurant", "Place", {"cuisine": "Italian"}, ConfidenceLevel.HIGH)
        ]
        
        self.sample_relationships = [
            ExtractedRelationship("John", "Pizza", "LIKES", {"intensity": "high"}, ConfidenceLevel.HIGH),
            ExtractedRelationship("John", "Restaurant", "VISITS", {"frequency": "weekly"}, ConfidenceLevel.MEDIUM)
        ]
    
    def test_initialization(self):
        """Test builder initialization"""
        self.assertIsNotNone(self.builder)
        self.assertEqual(len(self.builder.discovered_entities), 0)
        self.assertEqual(len(self.builder.discovered_relationships), 0)
    
    def test_pattern_analysis(self):
        """Test pattern analysis functionality"""
        analysis = self.builder.analyze_patterns(self.sample_entities, self.sample_relationships)
        
        self.assertIn("entity_types", analysis)
        self.assertIn("relationship_types", analysis)
        
        # Check entity type analysis
        entity_types = analysis["entity_types"]
        self.assertIn("Person", entity_types)
        self.assertIn("Food", entity_types)
        self.assertIn("Place", entity_types)
        
        # Check relationship type analysis
        relationship_types = analysis["relationship_types"]
        self.assertIn("LIKES", relationship_types)
        self.assertIn("VISITS", relationship_types)
    
    def test_dynamic_entity_model_creation(self):
        """Test dynamic entity model creation"""
        analysis = self.builder.analyze_patterns(self.sample_entities, self.sample_relationships)
        entity_models = self.builder.create_dynamic_entity_models(analysis)
        
        self.assertIn("Person", entity_models)
        self.assertIn("Food", entity_models)
        self.assertIn("Place", entity_models)
        
        # Test that the models are actual classes
        PersonModel = entity_models["Person"]
        self.assertTrue(hasattr(PersonModel, "name"))
        self.assertTrue(hasattr(PersonModel, "age"))
    
    def test_dynamic_edge_model_creation(self):
        """Test dynamic edge model creation"""
        analysis = self.builder.analyze_patterns(self.sample_entities, self.sample_relationships)
        edge_models = self.builder.create_dynamic_edge_models(analysis)
        
        self.assertIn("LIKES", edge_models)
        self.assertIn("VISITS", edge_models)
        
        # Test that the models are tuples with class and source-target pairs
        likes_model, likes_pairs = edge_models["LIKES"]
        self.assertTrue(hasattr(likes_model, "confidence_level"))
        self.assertTrue(hasattr(likes_model, "temporal_context"))


class TestSemanticRelationshipInferencer(unittest.TestCase):
    """Test the SemanticRelationshipInferencer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Mock spaCy to avoid dependency issues in tests
        with patch('spacy.load') as mock_spacy:
            mock_nlp = Mock()
            mock_spacy.return_value = mock_nlp
            self.inferencer = SemanticRelationshipInferencer()
    
    def test_initialization(self):
        """Test inferencer initialization"""
        self.assertIsNotNone(self.inferencer)
    
    @patch('spacy.load')
    def test_initialization_with_missing_model(self, mock_spacy):
        """Test initialization when spaCy model is missing"""
        mock_spacy.side_effect = OSError("Model not found")
        inferencer = SemanticRelationshipInferencer()
        self.assertIsNone(inferencer.nlp)


class TestDomainAgnosticKnowledgeGraph(unittest.TestCase):
    """Test the DomainAgnosticKnowledgeGraph class"""
    
    def setUp(self):
        """Set up test fixtures"""
        with patch('zep_cloud.client.Zep'), \
             patch('dynamic_relationship_extractor.DynamicRelationshipExtractor'), \
             patch('dynamic_relationship_extractor.SemanticRelationshipInferencer'):
            self.kg_system = DomainAgnosticKnowledgeGraph("fake_zep_key", "fake_openai_key")
    
    def test_initialization(self):
        """Test system initialization"""
        self.assertIsNotNone(self.kg_system)
        self.assertIsNotNone(self.kg_system.extractor)
        self.assertIsNotNone(self.kg_system.ontology_builder)
        self.assertIsNotNone(self.kg_system.semantic_inferencer)
        self.assertEqual(len(self.kg_system.processing_history), 0)
    
    def test_confidence_calculation(self):
        """Test confidence statistics calculation"""
        entities = [
            ExtractedEntity("A", "Type1", {}, ConfidenceLevel.HIGH),
            ExtractedEntity("B", "Type2", {}, ConfidenceLevel.MEDIUM),
            ExtractedEntity("C", "Type3", {}, ConfidenceLevel.LOW)
        ]
        
        relationships = [
            ExtractedRelationship("A", "B", "REL1", {}, ConfidenceLevel.HIGH),
            ExtractedRelationship("B", "C", "REL2", {}, ConfidenceLevel.MEDIUM)
        ]
        
        stats = self.kg_system._calculate_confidence_stats(entities, relationships)
        
        self.assertIn("entity_confidence", stats)
        self.assertIn("relationship_confidence", stats)
        self.assertIn("overall_confidence", stats)
        
        # Check entity confidence distribution
        self.assertEqual(stats["entity_confidence"]["high"], 1)
        self.assertEqual(stats["entity_confidence"]["medium"], 1)
        self.assertEqual(stats["entity_confidence"]["low"], 1)
        
        # Check relationship confidence distribution
        self.assertEqual(stats["relationship_confidence"]["high"], 1)
        self.assertEqual(stats["relationship_confidence"]["medium"], 1)
        self.assertEqual(stats["relationship_confidence"]["low"], 0)
    
    def test_overall_confidence_calculation(self):
        """Test overall confidence score calculation"""
        entities = [ExtractedEntity("A", "Type", {}, ConfidenceLevel.HIGH)]
        relationships = [ExtractedRelationship("A", "B", "REL", {}, ConfidenceLevel.MEDIUM)]
        
        confidence = self.kg_system._calculate_overall_confidence(entities, relationships)
        
        # High = 1.0, Medium = 0.6, so average should be 0.8
        self.assertAlmostEqual(confidence, 0.8, places=1)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def test_message_processing_structure(self):
        """Test that messages can be processed structurally"""
        messages = [
            Message(role="user", role_type="user", content="I love Italian food"),
            Message(role="assistant", role_type="assistant", content="What's your favorite dish?"),
            Message(role="user", role_type="user", content="Pizza is my favorite")
        ]
        
        # Test that we can combine message content
        full_text = " ".join([msg.content for msg in messages])
        self.assertIn("Italian food", full_text)
        self.assertIn("Pizza", full_text)
        self.assertEqual(len(messages), 3)


def run_tests():
    """Run all tests"""
    print("🧪 Running Dynamic Relationship Extraction System Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDynamicRelationshipExtractor,
        TestGenericOntologyBuilder,
        TestSemanticRelationshipInferencer,
        TestDomainAgnosticKnowledgeGraph,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 All tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
