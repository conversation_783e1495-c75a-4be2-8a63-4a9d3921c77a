"""
Dynamic Relationship Extraction System for Zep Knowledge Graphs

This module provides a comprehensive system for extracting entities and relationships
from conversational text without predefined schemas, building flexible ontologies,
and using semantic inference to identify implicit relationships.
"""

import os
import re
import json
import uuid
from typing import Dict, List, Tuple, Any, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging

# External dependencies
import spacy
from openai import OpenAI
from pydantic import Field

# Zep imports
from zep_cloud.client import Zep
from zep_cloud.types import Message
from zep_cloud import EntityEdgeSourceTarget
from zep_cloud.external_clients.ontology import EntityModel, EntityText, EntityInt, EdgeModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfidenceLevel(Enum):
    """Confidence levels for extracted relationships"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ExtractedEntity:
    """Represents an extracted entity with metadata"""
    name: str
    entity_type: str
    attributes: Dict[str, Any] = field(default_factory=dict)
    confidence: ConfidenceLevel = ConfidenceLevel.MEDIUM
    source_text: str = ""
    temporal_context: Optional[str] = None


@dataclass
class ExtractedRelationship:
    """Represents an extracted relationship with metadata"""
    source_entity: str
    target_entity: str
    relationship_type: str
    attributes: Dict[str, Any] = field(default_factory=dict)
    confidence: ConfidenceLevel = ConfidenceLevel.MEDIUM
    source_text: str = ""
    temporal_context: Optional[str] = None
    emotional_context: Optional[str] = None


class DynamicRelationshipExtractor:
    """
    Core class that uses LLM prompting to extract entities and relationships
    from conversational text without predefined schemas.
    """
    
    def __init__(self, openai_api_key: str, model: str = "gpt-4"):
        """Initialize the extractor with OpenAI client"""
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.model = model
        
        # Common relationship patterns for different domains
        self.relationship_patterns = {
            "preference": ["likes", "dislikes", "prefers", "enjoys", "hates", "loves"],
            "temporal": ["before", "after", "during", "since", "until", "when"],
            "social": ["friend", "family", "colleague", "spouse", "partner"],
            "experience": ["visited", "tried", "used", "bought", "booked"],
            "attribute": ["has", "is", "contains", "includes", "features"]
        }
    
    def extract_entities_and_relationships(self, text: str, context: str = "") -> Tuple[List[ExtractedEntity], List[ExtractedRelationship]]:
        """
        Extract entities and relationships from text using LLM prompting
        
        Args:
            text: The conversational text to analyze
            context: Additional context about the conversation domain
            
        Returns:
            Tuple of (entities, relationships)
        """
        prompt = self._build_extraction_prompt(text, context)
        
        try:
            response = self.openai_client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=2000
            )
            
            result = response.choices[0].message.content
            return self._parse_extraction_result(result, text)
            
        except Exception as e:
            logger.error(f"Error in LLM extraction: {e}")
            return [], []
    
    def _build_extraction_prompt(self, text: str, context: str = "") -> str:
        """Build the LLM prompt for entity and relationship extraction"""
        return f"""
You are an expert at extracting entities and relationships from conversational text. 
Analyze the following conversation and extract:

1. ENTITIES: People, places, objects, concepts, preferences, experiences, etc.
2. RELATIONSHIPS: How entities relate to each other, including temporal and emotional context

Text to analyze: "{text}"
Context: {context if context else "General conversation"}

Please return your analysis in the following JSON format:
{{
    "entities": [
        {{
            "name": "entity_name",
            "type": "entity_type",
            "attributes": {{"key": "value"}},
            "confidence": "high|medium|low",
            "temporal_context": "when this entity is relevant",
            "source_text": "exact text where found"
        }}
    ],
    "relationships": [
        {{
            "source": "source_entity",
            "target": "target_entity", 
            "type": "relationship_type",
            "attributes": {{"key": "value"}},
            "confidence": "high|medium|low",
            "temporal_context": "when this relationship applies",
            "emotional_context": "emotional sentiment if applicable",
            "source_text": "exact text where found"
        }}
    ]
}}

Focus on:
- Preferences and opinions (likes/dislikes)
- Temporal relationships (when, how often, duration)
- Social connections (family, friends, colleagues)
- Experiences and activities
- Changes over time
- Implicit relationships not explicitly stated

Be thorough but only extract relationships that are clearly supported by the text.
"""

    def _parse_extraction_result(self, result: str, original_text: str) -> Tuple[List[ExtractedEntity], List[ExtractedRelationship]]:
        """Parse the LLM response into structured entities and relationships"""
        try:
            # Clean up the result to extract JSON
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if not json_match:
                logger.warning("No JSON found in LLM response")
                return [], []
            
            data = json.loads(json_match.group())
            
            entities = []
            for entity_data in data.get("entities", []):
                entity = ExtractedEntity(
                    name=entity_data.get("name", ""),
                    entity_type=entity_data.get("type", "Unknown"),
                    attributes=entity_data.get("attributes", {}),
                    confidence=ConfidenceLevel(entity_data.get("confidence", "medium")),
                    source_text=entity_data.get("source_text", ""),
                    temporal_context=entity_data.get("temporal_context")
                )
                entities.append(entity)
            
            relationships = []
            for rel_data in data.get("relationships", []):
                relationship = ExtractedRelationship(
                    source_entity=rel_data.get("source", ""),
                    target_entity=rel_data.get("target", ""),
                    relationship_type=rel_data.get("type", "RELATED_TO"),
                    attributes=rel_data.get("attributes", {}),
                    confidence=ConfidenceLevel(rel_data.get("confidence", "medium")),
                    source_text=rel_data.get("source_text", ""),
                    temporal_context=rel_data.get("temporal_context"),
                    emotional_context=rel_data.get("emotional_context")
                )
                relationships.append(relationship)
            
            return entities, relationships
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from LLM response: {e}")
            return [], []
        except Exception as e:
            logger.error(f"Error parsing extraction result: {e}")
            return [], []
    
    def extract_temporal_patterns(self, text: str) -> Dict[str, Any]:
        """Extract temporal patterns from text"""
        temporal_patterns = {
            "frequency": re.findall(r'\b(always|never|often|sometimes|rarely|daily|weekly|monthly|yearly)\b', text.lower()),
            "duration": re.findall(r'\b(\d+\s*(minutes?|hours?|days?|weeks?|months?|years?))\b', text.lower()),
            "time_references": re.findall(r'\b(yesterday|today|tomorrow|last\s+\w+|next\s+\w+|ago|since)\b', text.lower()),
            "seasons": re.findall(r'\b(spring|summer|fall|autumn|winter)\b', text.lower())
        }
        
        return {k: list(set(v)) for k, v in temporal_patterns.items() if v}
    
    def extract_emotional_context(self, text: str) -> Dict[str, Any]:
        """Extract emotional and preference context from text"""
        positive_indicators = re.findall(r'\b(love|like|enjoy|prefer|favorite|amazing|great|wonderful|excellent)\b', text.lower())
        negative_indicators = re.findall(r'\b(hate|dislike|avoid|terrible|awful|bad|worst|never again)\b', text.lower())
        
        return {
            "positive_sentiment": list(set(positive_indicators)),
            "negative_sentiment": list(set(negative_indicators)),
            "overall_sentiment": "positive" if len(positive_indicators) > len(negative_indicators) else "negative" if negative_indicators else "neutral"
        }


class GenericOntologyBuilder:
    """
    Creates flexible entity and relationship models based on discovered patterns
    in conversations, adapting to any domain without predefined schemas.
    """

    def __init__(self):
        """Initialize the ontology builder"""
        self.discovered_entities = {}
        self.discovered_relationships = {}
        self.entity_patterns = {}
        self.relationship_patterns = {}

    def analyze_patterns(self, entities: List[ExtractedEntity], relationships: List[ExtractedRelationship]) -> Dict[str, Any]:
        """
        Analyze patterns in extracted entities and relationships to build flexible ontologies

        Args:
            entities: List of extracted entities
            relationships: List of extracted relationships

        Returns:
            Dictionary containing pattern analysis results
        """
        # Analyze entity patterns
        entity_types = {}
        for entity in entities:
            entity_type = entity.entity_type
            if entity_type not in entity_types:
                entity_types[entity_type] = {
                    "count": 0,
                    "attributes": set(),
                    "examples": [],
                    "confidence_distribution": {"high": 0, "medium": 0, "low": 0}
                }

            entity_types[entity_type]["count"] += 1
            entity_types[entity_type]["attributes"].update(entity.attributes.keys())
            entity_types[entity_type]["examples"].append(entity.name)
            entity_types[entity_type]["confidence_distribution"][entity.confidence.value] += 1

        # Analyze relationship patterns
        relationship_types = {}
        for relationship in relationships:
            rel_type = relationship.relationship_type
            if rel_type not in relationship_types:
                relationship_types[rel_type] = {
                    "count": 0,
                    "source_types": set(),
                    "target_types": set(),
                    "attributes": set(),
                    "confidence_distribution": {"high": 0, "medium": 0, "low": 0}
                }

            relationship_types[rel_type]["count"] += 1
            relationship_types[rel_type]["attributes"].update(relationship.attributes.keys())
            relationship_types[rel_type]["confidence_distribution"][relationship.confidence.value] += 1

            # Find entity types for source and target
            source_type = self._find_entity_type(relationship.source_entity, entities)
            target_type = self._find_entity_type(relationship.target_entity, entities)

            if source_type:
                relationship_types[rel_type]["source_types"].add(source_type)
            if target_type:
                relationship_types[rel_type]["target_types"].add(target_type)

        return {
            "entity_types": {k: {**v, "attributes": list(v["attributes"])} for k, v in entity_types.items()},
            "relationship_types": {k: {**v, "source_types": list(v["source_types"]), "target_types": list(v["target_types"]), "attributes": list(v["attributes"])} for k, v in relationship_types.items()}
        }

    def _find_entity_type(self, entity_name: str, entities: List[ExtractedEntity]) -> Optional[str]:
        """Find the type of an entity by name"""
        for entity in entities:
            if entity.name.lower() == entity_name.lower():
                return entity.entity_type
        return None

    def create_dynamic_entity_models(self, pattern_analysis: Dict[str, Any]) -> Dict[str, type]:
        """
        Create dynamic EntityModel classes based on discovered patterns

        Args:
            pattern_analysis: Results from analyze_patterns()

        Returns:
            Dictionary mapping entity type names to EntityModel classes
        """
        entity_models = {}

        for entity_type, pattern_data in pattern_analysis["entity_types"].items():
            # Create dynamic attributes based on discovered patterns
            class_attributes = {}

            # Add common attributes
            class_attributes["name"] = EntityText(description=f"The name of the {entity_type.lower()}", default=None)

            # Add discovered attributes
            for attr_name in pattern_data["attributes"]:
                if attr_name not in ["name"]:  # Avoid duplicates
                    # Determine attribute type based on common patterns
                    if any(keyword in attr_name.lower() for keyword in ["age", "count", "number", "rating", "score"]):
                        class_attributes[attr_name] = EntityInt(description=f"The {attr_name} of the {entity_type.lower()}", default=None)
                    else:
                        class_attributes[attr_name] = EntityText(description=f"The {attr_name} of the {entity_type.lower()}", default=None)

            # Add temporal and emotional context attributes
            class_attributes["temporal_context"] = EntityText(description="When this entity is relevant", default=None)
            class_attributes["confidence_level"] = EntityText(description="Confidence level of entity extraction", default=None)

            # Create the dynamic class
            entity_class = type(
                entity_type,
                (EntityModel,),
                {
                    **class_attributes,
                    "__doc__": f"Dynamically created entity model for {entity_type} based on conversation patterns."
                }
            )

            entity_models[entity_type] = entity_class

        return entity_models

    def create_dynamic_edge_models(self, pattern_analysis: Dict[str, Any]) -> Dict[str, Tuple[type, List]]:
        """
        Create dynamic EdgeModel classes based on discovered relationship patterns

        Args:
            pattern_analysis: Results from analyze_patterns()

        Returns:
            Dictionary mapping relationship type names to (EdgeModel class, source-target pairs)
        """
        edge_models = {}

        for rel_type, pattern_data in pattern_analysis["relationship_types"].items():
            # Create dynamic attributes based on discovered patterns
            class_attributes = {}

            # Add discovered attributes
            for attr_name in pattern_data["attributes"]:
                if any(keyword in attr_name.lower() for keyword in ["date", "time", "when"]):
                    class_attributes[attr_name] = EntityText(description=f"Temporal information for {rel_type}", default=None)
                elif any(keyword in attr_name.lower() for keyword in ["count", "number", "rating", "score"]):
                    class_attributes[attr_name] = EntityInt(description=f"Numeric value for {rel_type}", default=None)
                else:
                    class_attributes[attr_name] = EntityText(description=f"Additional information for {rel_type}", default=None)

            # Add standard relationship attributes
            class_attributes["confidence_level"] = EntityText(description="Confidence level of relationship extraction", default=None)
            class_attributes["temporal_context"] = EntityText(description="When this relationship applies", default=None)
            class_attributes["emotional_context"] = EntityText(description="Emotional context of the relationship", default=None)
            class_attributes["source_text"] = EntityText(description="Original text where relationship was found", default=None)

            # Create the dynamic class
            edge_class = type(
                rel_type.replace(" ", "").replace("-", ""),
                (EdgeModel,),
                {
                    **class_attributes,
                    "__doc__": f"Dynamically created edge model for {rel_type} relationships."
                }
            )

            # Create source-target pairs
            source_target_pairs = []
            for source_type in pattern_data["source_types"]:
                for target_type in pattern_data["target_types"]:
                    source_target_pairs.append(EntityEdgeSourceTarget(source=source_type, target=target_type))

            # If no specific types found, allow User as source
            if not source_target_pairs:
                source_target_pairs.append(EntityEdgeSourceTarget(source="User", target="Entity"))

            edge_models[rel_type] = (edge_class, source_target_pairs)

        return edge_models


class SemanticRelationshipInferencer:
    """
    Uses NLP techniques like spaCy dependency parsing to find implicit relationships
    beyond explicit mentions in conversational text.
    """

    def __init__(self, spacy_model: str = "en_core_web_sm"):
        """Initialize the semantic inferencer with spaCy model"""
        try:
            self.nlp = spacy.load(spacy_model)
        except OSError:
            logger.warning(f"spaCy model {spacy_model} not found. Please install it with: python -m spacy download {spacy_model}")
            self.nlp = None

    def infer_implicit_relationships(self, text: str, explicit_entities: List[ExtractedEntity]) -> List[ExtractedRelationship]:
        """
        Infer implicit relationships using dependency parsing and semantic analysis

        Args:
            text: The conversational text to analyze
            explicit_entities: Already extracted entities to build upon

        Returns:
            List of inferred relationships
        """
        if not self.nlp:
            return []

        doc = self.nlp(text)
        implicit_relationships = []

        # Extract entity mentions from text
        entity_mentions = self._find_entity_mentions(doc, explicit_entities)

        # Analyze dependency relationships
        implicit_relationships.extend(self._analyze_dependencies(doc, entity_mentions))

        # Analyze co-occurrence patterns
        implicit_relationships.extend(self._analyze_cooccurrence(doc, entity_mentions))

        # Analyze temporal relationships
        implicit_relationships.extend(self._analyze_temporal_relationships(doc, entity_mentions))

        # Analyze causal relationships
        implicit_relationships.extend(self._analyze_causal_relationships(doc, entity_mentions))

        return implicit_relationships

    def _find_entity_mentions(self, doc, explicit_entities: List[ExtractedEntity]) -> Dict[str, List[spacy.tokens.Span]]:
        """Find mentions of entities in the text"""
        entity_mentions = {}

        # Find named entities using spaCy NER
        for ent in doc.ents:
            entity_name = ent.text.lower()
            if entity_name not in entity_mentions:
                entity_mentions[entity_name] = []
            entity_mentions[entity_name].append(ent)

        # Find mentions of explicit entities
        for entity in explicit_entities:
            entity_name = entity.name.lower()
            if entity_name not in entity_mentions:
                entity_mentions[entity_name] = []

            # Simple string matching (could be improved with fuzzy matching)
            for token in doc:
                if entity_name in token.text.lower():
                    span = doc[token.i:token.i+1]
                    entity_mentions[entity_name].append(span)

        return entity_mentions

    def _analyze_dependencies(self, doc, entity_mentions: Dict[str, List]) -> List[ExtractedRelationship]:
        """Analyze syntactic dependencies to infer relationships"""
        relationships = []

        for token in doc:
            # Look for subject-verb-object patterns
            if token.dep_ == "nsubj" and token.head.pos_ == "VERB":
                subject = token.text
                verb = token.head.text

                # Find objects
                for child in token.head.children:
                    if child.dep_ in ["dobj", "pobj", "attr"]:
                        obj = child.text

                        # Create relationship
                        rel = ExtractedRelationship(
                            source_entity=subject,
                            target_entity=obj,
                            relationship_type=f"ACTION_{verb.upper()}",
                            confidence=ConfidenceLevel.MEDIUM,
                            source_text=f"{subject} {verb} {obj}",
                            attributes={"dependency_pattern": "subject-verb-object"}
                        )
                        relationships.append(rel)

        return relationships

    def _analyze_cooccurrence(self, doc, entity_mentions: Dict[str, List]) -> List[ExtractedRelationship]:
        """Analyze entity co-occurrence patterns to infer relationships"""
        relationships = []
        entity_names = list(entity_mentions.keys())

        # Look for entities that appear in the same sentence
        for sent in doc.sents:
            sent_entities = []
            for entity_name in entity_names:
                for mention in entity_mentions[entity_name]:
                    if mention.start >= sent.start and mention.end <= sent.end:
                        sent_entities.append(entity_name)

            # Create co-occurrence relationships
            for i, entity1 in enumerate(sent_entities):
                for entity2 in sent_entities[i+1:]:
                    if entity1 != entity2:
                        rel = ExtractedRelationship(
                            source_entity=entity1,
                            target_entity=entity2,
                            relationship_type="CO_OCCURS_WITH",
                            confidence=ConfidenceLevel.LOW,
                            source_text=sent.text,
                            attributes={"pattern": "co-occurrence"}
                        )
                        relationships.append(rel)

        return relationships

    def _analyze_temporal_relationships(self, doc, entity_mentions: Dict[str, List]) -> List[ExtractedRelationship]:
        """Analyze temporal relationships between entities"""
        relationships = []

        # Look for temporal indicators
        temporal_indicators = ["before", "after", "during", "while", "when", "since", "until"]

        for token in doc:
            if token.text.lower() in temporal_indicators:
                # Find entities before and after the temporal indicator
                left_entities = []
                right_entities = []

                # Simple approach: look for entities in a window around the temporal indicator
                window_size = 5
                start_idx = max(0, token.i - window_size)
                end_idx = min(len(doc), token.i + window_size)

                for entity_name, mentions in entity_mentions.items():
                    for mention in mentions:
                        if start_idx <= mention.start < token.i:
                            left_entities.append(entity_name)
                        elif token.i < mention.start <= end_idx:
                            right_entities.append(entity_name)

                # Create temporal relationships
                for left_entity in left_entities:
                    for right_entity in right_entities:
                        rel = ExtractedRelationship(
                            source_entity=left_entity,
                            target_entity=right_entity,
                            relationship_type=f"TEMPORAL_{token.text.upper()}",
                            confidence=ConfidenceLevel.MEDIUM,
                            source_text=doc[start_idx:end_idx].text,
                            temporal_context=token.text,
                            attributes={"temporal_indicator": token.text}
                        )
                        relationships.append(rel)

        return relationships

    def _analyze_causal_relationships(self, doc, entity_mentions: Dict[str, List]) -> List[ExtractedRelationship]:
        """Analyze causal relationships between entities"""
        relationships = []

        # Look for causal indicators
        causal_indicators = ["because", "since", "due to", "caused by", "leads to", "results in"]

        for token in doc:
            if any(indicator in doc[max(0, token.i-2):token.i+3].text.lower() for indicator in causal_indicators):
                # Find entities that might be causally related
                window_size = 7
                start_idx = max(0, token.i - window_size)
                end_idx = min(len(doc), token.i + window_size)

                window_entities = []
                for entity_name, mentions in entity_mentions.items():
                    for mention in mentions:
                        if start_idx <= mention.start <= end_idx:
                            window_entities.append(entity_name)

                # Create causal relationships (simplified approach)
                if len(window_entities) >= 2:
                    for i in range(len(window_entities) - 1):
                        rel = ExtractedRelationship(
                            source_entity=window_entities[i],
                            target_entity=window_entities[i + 1],
                            relationship_type="CAUSES",
                            confidence=ConfidenceLevel.LOW,
                            source_text=doc[start_idx:end_idx].text,
                            attributes={"causal_pattern": "inferred"}
                        )
                        relationships.append(rel)

        return relationships
