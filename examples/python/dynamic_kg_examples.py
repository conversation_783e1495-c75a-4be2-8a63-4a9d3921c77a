"""
Dynamic Knowledge Graph Examples

This module demonstrates the dynamic relationship extraction system working
across different conversational domains: travel, shopping, and healthcare.
"""

import os
import uuid
from dotenv import find_dotenv, load_dotenv
from zep_cloud.types import Message

from domain_agnostic_knowledge_graph import DomainAgnosticKnowledgeGraph

# Load environment variables
load_dotenv(dotenv_path=find_dotenv())

def create_travel_conversation():
    """Create a sample travel planning conversation"""
    return [
        Message(role="user", role_type="user", content="Hi! I'm planning a trip to Japan with my wife <PERSON> next spring. We love authentic food experiences and she's really into photography."),
        Message(role="assistant", role_type="assistant", content="That sounds wonderful! Japan in spring is perfect for cherry blossoms, which <PERSON> will love photographing. Are you interested in traditional kaiseki dining experiences?"),
        Message(role="user", role_type="user", content="Yes! We tried kaiseki in Kyoto 3 years ago and loved it. <PERSON> got amazing photos of the presentation. We're thinking of visiting Tokyo and Osaka this time."),
        Message(role="assistant", role_type="assistant", content="Excellent choices! For Tokyo, I recommend <PERSON><PERSON><PERSON><PERSON> for kaiseki - it's been <PERSON><PERSON> starred for years. In Osaka, the street food scene is incredible for photography and authentic flavors."),
        Message(role="user", role_type="user", content="Perfect! We usually stay in boutique hotels. <PERSON> needs good lighting for editing her photos, and I prefer places with traditional architecture."),
        Message(role="assistant", role_type="assistant", content="I have the perfect recommendations! Hoshinoya Tokyo combines traditional ryokan style with modern amenities and excellent lighting. For Osaka, consider Hotel The Celestine - it has beautiful traditional elements and great workspace areas."),
        Message(role="user", role_type="user", content="That sounds ideal. We're also hoping to take a cooking class together. Sarah wants to learn traditional techniques, and I'm curious about the knife skills."),
        Message(role="assistant", role_type="assistant", content="Wonderful! I recommend the Tsukiji Cooking School in Tokyo - they offer hands-on classes focusing on traditional techniques and knife skills. It's very popular with couples who want to learn together.")
    ]

def create_shopping_conversation():
    """Create a sample shopping conversation"""
    return [
        Message(role="user", role_type="user", content="I'm looking for a new laptop for my graphic design work. I currently use a MacBook Pro from 2019, but it's getting slow with large Photoshop files."),
        Message(role="assistant", role_type="assistant", content="I understand the frustration with slow performance on large files! What's your budget range, and do you have a preference for Mac or are you open to Windows machines?"),
        Message(role="user", role_type="user", content="I'd prefer to stick with Mac since I'm used to the workflow, but I'm open to Windows if the performance is significantly better. Budget is around $3000-4000."),
        Message(role="assistant", role_type="assistant", content="Great budget range! The new MacBook Pro 16-inch with M3 Max chip would be perfect for your Photoshop work. It handles large files incredibly well and has excellent color accuracy for design work."),
        Message(role="user", role_type="user", content="That sounds promising. I also need a good external monitor. I currently have a 24-inch Dell, but I'd like something larger with better color reproduction."),
        Message(role="assistant", role_type="assistant", content="For professional design work, I'd recommend the Apple Studio Display 27-inch or the LG UltraFine 32-inch. Both offer excellent color accuracy and integrate well with MacBooks."),
        Message(role="user", role_type="user", content="The LG sounds interesting. I've had good experiences with LG monitors before. My old LG lasted 6 years without any issues."),
        Message(role="assistant", role_type="assistant", content="LG has excellent build quality! The UltraFine 32-inch supports P3 wide color gamut and has Thunderbolt connectivity, so you can charge your MacBook while using it as a display."),
        Message(role="user", role_type="user", content="Perfect! I also need to upgrade my storage setup. I currently use external hard drives, but I'm thinking about a NAS system for better organization and backup."),
        Message(role="assistant", role_type="assistant", content="A NAS is a smart choice for design work! The Synology DS220+ is popular with creatives - it's reliable, has good software, and you can set up automatic backups from your MacBook.")
    ]

def create_healthcare_conversation():
    """Create a sample healthcare conversation"""
    return [
        Message(role="user", role_type="user", content="I've been having trouble sleeping lately. I usually go to bed around 11 PM but lie awake for hours. This started about 3 weeks ago when I changed jobs."),
        Message(role="assistant", role_type="assistant", content="I understand how frustrating sleep issues can be, especially during job transitions. Can you tell me about your current bedtime routine and any changes since starting your new position?"),
        Message(role="user", role_type="user", content="My new job is more stressful - I'm a project manager now instead of a developer. I find myself thinking about work tasks even after I get home. I used to fall asleep easily."),
        Message(role="assistant", role_type="assistant", content="The transition from developer to project manager is significant and can definitely impact sleep. Stress and racing thoughts are common causes of insomnia. Have you tried any relaxation techniques?"),
        Message(role="user", role_type="user", content="Not really. I usually just scroll my phone in bed, which probably doesn't help. I also drink coffee throughout the day - maybe 4-5 cups since I'm more tired now."),
        Message(role="assistant", role_type="assistant", content="Phone screens and caffeine can definitely interfere with sleep quality. The blue light from phones can suppress melatonin production, and caffeine stays in your system for 6-8 hours."),
        Message(role="user", role_type="user", content="I didn't realize caffeine lasted that long! I usually have my last coffee around 3 PM. Should I cut back earlier?"),
        Message(role="assistant", role_type="assistant", content="Yes, try limiting caffeine after 2 PM, or even earlier if you're sensitive. Also, consider replacing evening phone time with reading or meditation. Have you considered talking to someone about the work stress?"),
        Message(role="user", role_type="user", content="My company offers counseling services through our health plan. I've been thinking about it but wasn't sure if it would help with sleep issues."),
        Message(role="assistant", role_type="assistant", content="Counseling can be very helpful for work-related stress and sleep issues. A therapist can teach you stress management techniques and help you process the job transition. It's worth exploring!")
    ]

def run_travel_example():
    """Run the travel domain example"""
    print("\n" + "="*60)
    print("TRAVEL DOMAIN EXAMPLE")
    print("="*60)
    
    # Initialize the system
    kg_system = DomainAgnosticKnowledgeGraph(
        zep_api_key=os.environ.get("ZEP_API_KEY"),
        openai_api_key=os.environ.get("OPENAI_API_KEY")
    )
    
    # Process travel conversation
    travel_messages = create_travel_conversation()
    result = kg_system.process_conversation(
        messages=travel_messages,
        context="Travel planning conversation between user and travel assistant"
    )
    
    print(f"\nProcessed {result.processing_metadata['message_count']} messages")
    print(f"Extracted {len(result.entities)} entities and {len(result.relationships)} relationships")
    
    print("\nTop Entity Types:")
    for entity in result.entities[:5]:
        print(f"  - {entity.name} ({entity.entity_type}) - Confidence: {entity.confidence.value}")
    
    print("\nTop Relationships:")
    for rel in result.relationships[:5]:
        print(f"  - {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity} (Confidence: {rel.confidence.value})")
    
    print(f"\nDynamic Ontology Created:")
    print(f"  - Entity Types: {list(result.ontology_entities.keys())}")
    print(f"  - Relationship Types: {list(result.ontology_edges.keys())}")
    
    # Integrate with Zep
    user_id = f"travel-user-{uuid.uuid4().hex[:8]}"
    session_id = f"travel-session-{uuid.uuid4().hex[:8]}"
    
    success = kg_system.integrate_with_zep(result, user_id, session_id)
    print(f"\nZep Integration: {'Success' if success else 'Failed'}")
    
    return result

def run_shopping_example():
    """Run the shopping domain example"""
    print("\n" + "="*60)
    print("SHOPPING DOMAIN EXAMPLE")
    print("="*60)
    
    # Initialize the system
    kg_system = DomainAgnosticKnowledgeGraph(
        zep_api_key=os.environ.get("ZEP_API_KEY"),
        openai_api_key=os.environ.get("OPENAI_API_KEY")
    )
    
    # Process shopping conversation
    shopping_messages = create_shopping_conversation()
    result = kg_system.process_conversation(
        messages=shopping_messages,
        context="Technology shopping conversation for graphic design equipment"
    )
    
    print(f"\nProcessed {result.processing_metadata['message_count']} messages")
    print(f"Extracted {len(result.entities)} entities and {len(result.relationships)} relationships")
    
    print("\nTop Entity Types:")
    for entity in result.entities[:5]:
        print(f"  - {entity.name} ({entity.entity_type}) - Confidence: {entity.confidence.value}")
    
    print("\nTop Relationships:")
    for rel in result.relationships[:5]:
        print(f"  - {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity} (Confidence: {rel.confidence.value})")
    
    print(f"\nDynamic Ontology Created:")
    print(f"  - Entity Types: {list(result.ontology_entities.keys())}")
    print(f"  - Relationship Types: {list(result.ontology_edges.keys())}")
    
    # Integrate with Zep
    user_id = f"shopping-user-{uuid.uuid4().hex[:8]}"
    session_id = f"shopping-session-{uuid.uuid4().hex[:8]}"
    
    success = kg_system.integrate_with_zep(result, user_id, session_id)
    print(f"\nZep Integration: {'Success' if success else 'Failed'}")
    
    return result

def run_healthcare_example():
    """Run the healthcare domain example"""
    print("\n" + "="*60)
    print("HEALTHCARE DOMAIN EXAMPLE")
    print("="*60)
    
    # Initialize the system
    kg_system = DomainAgnosticKnowledgeGraph(
        zep_api_key=os.environ.get("ZEP_API_KEY"),
        openai_api_key=os.environ.get("OPENAI_API_KEY")
    )
    
    # Process healthcare conversation
    healthcare_messages = create_healthcare_conversation()
    result = kg_system.process_conversation(
        messages=healthcare_messages,
        context="Healthcare consultation about sleep issues and stress management"
    )
    
    print(f"\nProcessed {result.processing_metadata['message_count']} messages")
    print(f"Extracted {len(result.entities)} entities and {len(result.relationships)} relationships")
    
    print("\nTop Entity Types:")
    for entity in result.entities[:5]:
        print(f"  - {entity.name} ({entity.entity_type}) - Confidence: {entity.confidence.value}")
    
    print("\nTop Relationships:")
    for rel in result.relationships[:5]:
        print(f"  - {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity} (Confidence: {rel.confidence.value})")
    
    print(f"\nDynamic Ontology Created:")
    print(f"  - Entity Types: {list(result.ontology_entities.keys())}")
    print(f"  - Relationship Types: {list(result.ontology_edges.keys())}")
    
    # Integrate with Zep
    user_id = f"healthcare-user-{uuid.uuid4().hex[:8]}"
    session_id = f"healthcare-session-{uuid.uuid4().hex[:8]}"
    
    success = kg_system.integrate_with_zep(result, user_id, session_id)
    print(f"\nZep Integration: {'Success' if success else 'Failed'}")
    
    return result

def run_comparative_analysis():
    """Run all examples and provide comparative analysis"""
    print("\n" + "="*60)
    print("COMPARATIVE DOMAIN ANALYSIS")
    print("="*60)
    
    # Run all examples
    travel_result = run_travel_example()
    shopping_result = run_shopping_example()
    healthcare_result = run_healthcare_example()
    
    # Analyze results
    results = [travel_result, shopping_result, healthcare_result]
    domains = ["Travel", "Shopping", "Healthcare"]
    
    print("\n" + "="*60)
    print("CROSS-DOMAIN COMPARISON")
    print("="*60)
    
    for i, (domain, result) in enumerate(zip(domains, results)):
        print(f"\n{domain} Domain:")
        print(f"  Entities: {len(result.entities)}")
        print(f"  Relationships: {len(result.relationships)}")
        print(f"  Ontology Entity Types: {len(result.ontology_entities)}")
        print(f"  Ontology Edge Types: {len(result.ontology_edges)}")
        print(f"  Overall Confidence: {result.confidence_stats['overall_confidence']:.2f}")
    
    # Initialize system for insights
    kg_system = DomainAgnosticKnowledgeGraph(
        zep_api_key=os.environ.get("ZEP_API_KEY"),
        openai_api_key=os.environ.get("OPENAI_API_KEY")
    )
    
    insights = kg_system.get_domain_insights(results)
    
    print(f"\nOverall Insights:")
    print(f"  Total Conversations: {insights['total_conversations_processed']}")
    print(f"  Total Entities: {insights['total_entities_extracted']}")
    print(f"  Total Relationships: {insights['total_relationships_extracted']}")
    print(f"  Avg Entities/Conversation: {insights['average_entities_per_conversation']:.1f}")
    print(f"  Avg Relationships/Conversation: {insights['average_relationships_per_conversation']:.1f}")
    
    print(f"\nMost Common Entity Types:")
    for entity_type, count in insights['most_common_entity_types'][:5]:
        print(f"  - {entity_type}: {count}")
    
    print(f"\nMost Common Relationship Types:")
    for rel_type, count in insights['most_common_relationship_types'][:5]:
        print(f"  - {rel_type}: {count}")

if __name__ == "__main__":
    # Check for required environment variables
    if not os.environ.get("ZEP_API_KEY"):
        print("Error: ZEP_API_KEY environment variable not set")
        exit(1)
    
    if not os.environ.get("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY environment variable not set")
        exit(1)
    
    print("Dynamic Relationship Extraction System - Domain Examples")
    print("This system demonstrates domain-agnostic knowledge graph construction")
    
    # Run comparative analysis
    run_comparative_analysis()
