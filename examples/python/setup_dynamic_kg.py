#!/usr/bin/env python3
"""
Setup script for the Dynamic Relationship Extraction System

This script helps users set up the environment and dependencies
for the dynamic knowledge graph system.
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_pip():
    """Check if pip is available"""
    try:
        import pip
        print("✅ pip is available")
        return True
    except ImportError:
        print("❌ Error: pip is not available")
        return False

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing requirements...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print("❌ Error: requirements.txt not found")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def download_spacy_model():
    """Download the spaCy English model"""
    print("\n🔤 Downloading spaCy English model...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "spacy", "download", "en_core_web_sm"
        ])
        print("✅ spaCy model downloaded successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error downloading spaCy model: {e}")
        print("You can try downloading manually with: python -m spacy download en_core_web_sm")
        return False

def check_environment_variables():
    """Check if required environment variables are set"""
    print("\n🔑 Checking environment variables...")
    
    required_vars = ["ZEP_API_KEY", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if os.environ.get(var):
            print(f"✅ {var} is set")
        else:
            print(f"❌ {var} is not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please set them before running the examples:")
        for var in missing_vars:
            print(f"  export {var}='your_{var.lower()}'")
        return False
    
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing imports...")
    
    required_modules = [
        ("openai", "OpenAI"),
        ("spacy", "spaCy"),
        ("zep_cloud", "Zep Cloud"),
        ("pydantic", "Pydantic")
    ]
    
    failed_imports = []
    
    for module_name, display_name in required_modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {display_name} imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import {display_name}: {e}")
            failed_imports.append(module_name)
    
    # Test spaCy model specifically
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        print("✅ spaCy English model loaded successfully")
    except OSError:
        print("❌ spaCy English model not found")
        failed_imports.append("en_core_web_sm")
    except Exception as e:
        print(f"❌ Error loading spaCy model: {e}")
        failed_imports.append("en_core_web_sm")
    
    return len(failed_imports) == 0

def test_system_functionality():
    """Test basic system functionality"""
    print("\n🔧 Testing system functionality...")
    
    # Check if we can create the main classes
    try:
        from dynamic_relationship_extractor import DynamicRelationshipExtractor, GenericOntologyBuilder, SemanticRelationshipInferencer
        from domain_agnostic_knowledge_graph import DomainAgnosticKnowledgeGraph
        
        print("✅ All system classes can be imported")
        
        # Test basic initialization (without API keys for now)
        try:
            ontology_builder = GenericOntologyBuilder()
            semantic_inferencer = SemanticRelationshipInferencer()
            print("✅ Core components can be initialized")
        except Exception as e:
            print(f"❌ Error initializing components: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Error importing system classes: {e}")
        return False

def create_env_file():
    """Create a sample .env file"""
    print("\n📝 Creating sample .env file...")
    
    env_file = Path(__file__).parent / ".env.example"
    env_content = """# Environment variables for Dynamic Relationship Extraction System
# Copy this file to .env and fill in your actual API keys

# Zep Cloud API Key
# Get yours at: https://www.getzep.com/
ZEP_API_KEY=your_zep_api_key_here

# OpenAI API Key  
# Get yours at: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print(f"✅ Sample .env file created at {env_file}")
        print("   Copy it to .env and add your actual API keys")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Dynamic Relationship Extraction System Setup")
    print("=" * 50)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Check pip
    if not check_pip():
        success = False
    
    if not success:
        print("\n❌ Setup failed due to system requirements")
        return False
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Download spaCy model
    if not download_spacy_model():
        success = False
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test system functionality
    if not test_system_functionality():
        success = False
    
    # Create sample env file
    create_env_file()
    
    # Check environment variables
    env_vars_ok = check_environment_variables()
    
    print("\n" + "=" * 50)
    if success and env_vars_ok:
        print("🎉 Setup completed successfully!")
        print("\nYou can now run the examples:")
        print("  python dynamic_kg_examples.py")
    elif success:
        print("⚠️  Setup mostly completed!")
        print("Please set the required environment variables and try again.")
    else:
        print("❌ Setup failed!")
        print("Please resolve the errors above and run setup again.")
    
    print("\n📚 For more information, see README_dynamic_kg.md")
    
    return success and env_vars_ok

if __name__ == "__main__":
    main()
