"""
Domain Agnostic Knowledge Graph System

This module provides the main orchestrating class that coordinates the entire
dynamic relationship extraction process and integrates with Zep's graph ontology system.
"""

import os
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# Zep imports
from zep_cloud.client import Zep
from zep_cloud.types import Message
from zep_cloud import EntityEdgeSourceTarget

# Local imports
from dynamic_relationship_extractor import (
    DynamicRelationshipExtractor,
    GenericOntologyBuilder,
    SemanticRelationshipInferencer,
    ExtractedEntity,
    ExtractedRelationship,
    ConfidenceLevel
)

logger = logging.getLogger(__name__)


@dataclass
class ProcessingResult:
    """Results from processing a conversation"""
    entities: List[ExtractedEntity]
    relationships: List[ExtractedRelationship]
    ontology_entities: Dict[str, type]
    ontology_edges: Dict[str, Tuple[type, List]]
    confidence_stats: Dict[str, Any]
    processing_metadata: Dict[str, Any]


class DomainAgnosticKnowledgeGraph:
    """
    Main orchestrating class that coordinates the entire dynamic relationship
    extraction process and integrates with Zep's graph ontology system.
    """
    
    def __init__(self, zep_api_key: str, openai_api_key: str, spacy_model: str = "en_core_web_sm"):
        """
        Initialize the domain agnostic knowledge graph system
        
        Args:
            zep_api_key: API key for Zep Cloud
            openai_api_key: API key for OpenAI
            spacy_model: spaCy model to use for NLP processing
        """
        self.zep_client = Zep(api_key=zep_api_key)
        
        # Initialize components
        self.extractor = DynamicRelationshipExtractor(openai_api_key)
        self.ontology_builder = GenericOntologyBuilder()
        self.semantic_inferencer = SemanticRelationshipInferencer(spacy_model)
        
        # Track processing history
        self.processing_history = []
        self.global_patterns = {}
    
    def process_conversation(self, messages: List[Message], context: str = "", 
                           user_id: str = None, session_id: str = None) -> ProcessingResult:
        """
        Process a complete conversation to extract entities, relationships, and build ontologies
        
        Args:
            messages: List of conversation messages
            context: Optional context about the conversation domain
            user_id: Optional user ID for Zep integration
            session_id: Optional session ID for Zep integration
            
        Returns:
            ProcessingResult containing all extracted information
        """
        logger.info(f"Processing conversation with {len(messages)} messages")
        
        # Combine all message content
        full_text = " ".join([msg.content for msg in messages if hasattr(msg, 'content')])
        
        # Step 1: Extract entities and relationships using LLM
        logger.info("Extracting entities and relationships with LLM...")
        entities, relationships = self.extractor.extract_entities_and_relationships(full_text, context)
        
        # Step 2: Infer additional implicit relationships using NLP
        logger.info("Inferring implicit relationships with NLP...")
        implicit_relationships = self.semantic_inferencer.infer_implicit_relationships(full_text, entities)
        all_relationships = relationships + implicit_relationships
        
        # Step 3: Analyze patterns and build dynamic ontologies
        logger.info("Building dynamic ontologies...")
        pattern_analysis = self.ontology_builder.analyze_patterns(entities, all_relationships)
        ontology_entities = self.ontology_builder.create_dynamic_entity_models(pattern_analysis)
        ontology_edges = self.ontology_builder.create_dynamic_edge_models(pattern_analysis)
        
        # Step 4: Calculate confidence statistics
        confidence_stats = self._calculate_confidence_stats(entities, all_relationships)
        
        # Step 5: Create processing metadata
        processing_metadata = {
            "message_count": len(messages),
            "text_length": len(full_text),
            "entity_count": len(entities),
            "relationship_count": len(all_relationships),
            "implicit_relationship_count": len(implicit_relationships),
            "ontology_entity_types": len(ontology_entities),
            "ontology_edge_types": len(ontology_edges),
            "context": context,
            "processing_timestamp": str(uuid.uuid4())
        }
        
        # Create result
        result = ProcessingResult(
            entities=entities,
            relationships=all_relationships,
            ontology_entities=ontology_entities,
            ontology_edges=ontology_edges,
            confidence_stats=confidence_stats,
            processing_metadata=processing_metadata
        )
        
        # Store in processing history
        self.processing_history.append(result)
        
        logger.info(f"Processing complete: {len(entities)} entities, {len(all_relationships)} relationships")
        return result
    
    def integrate_with_zep(self, result: ProcessingResult, user_id: str, session_id: str = None) -> bool:
        """
        Integrate the processing results with Zep's knowledge graph system
        
        Args:
            result: ProcessingResult from process_conversation
            user_id: User ID for Zep
            session_id: Optional session ID for Zep
            
        Returns:
            True if integration was successful
        """
        try:
            logger.info("Integrating with Zep knowledge graph...")
            
            # Set the dynamic ontology in Zep
            self.zep_client.graph.set_ontology(
                entities=result.ontology_entities,
                edges=result.ontology_edges
            )
            
            # Create or update user
            try:
                self.zep_client.user.add(
                    user_id=user_id,
                    first_name="Dynamic",
                    last_name="User",
                    email=f"{user_id}@example.com"
                )
            except Exception as e:
                logger.debug(f"User might already exist: {e}")
            
            # Create session if provided
            if session_id:
                try:
                    self.zep_client.memory.add_session(
                        session_id=session_id,
                        user_id=user_id
                    )
                except Exception as e:
                    logger.debug(f"Session might already exist: {e}")
            
            logger.info("Zep integration successful")
            return True
            
        except Exception as e:
            logger.error(f"Failed to integrate with Zep: {e}")
            return False
    
    def _calculate_confidence_stats(self, entities: List[ExtractedEntity], 
                                  relationships: List[ExtractedRelationship]) -> Dict[str, Any]:
        """Calculate confidence statistics for extracted data"""
        entity_confidence = {"high": 0, "medium": 0, "low": 0}
        relationship_confidence = {"high": 0, "medium": 0, "low": 0}
        
        for entity in entities:
            entity_confidence[entity.confidence.value] += 1
        
        for relationship in relationships:
            relationship_confidence[relationship.confidence.value] += 1
        
        total_entities = len(entities)
        total_relationships = len(relationships)
        
        return {
            "entity_confidence": entity_confidence,
            "relationship_confidence": relationship_confidence,
            "entity_confidence_percentages": {
                k: (v / total_entities * 100) if total_entities > 0 else 0 
                for k, v in entity_confidence.items()
            },
            "relationship_confidence_percentages": {
                k: (v / total_relationships * 100) if total_relationships > 0 else 0 
                for k, v in relationship_confidence.items()
            },
            "overall_confidence": self._calculate_overall_confidence(entities, relationships)
        }
    
    def _calculate_overall_confidence(self, entities: List[ExtractedEntity], 
                                    relationships: List[ExtractedRelationship]) -> float:
        """Calculate overall confidence score"""
        confidence_weights = {"high": 1.0, "medium": 0.6, "low": 0.3}
        
        total_score = 0
        total_items = 0
        
        for entity in entities:
            total_score += confidence_weights[entity.confidence.value]
            total_items += 1
        
        for relationship in relationships:
            total_score += confidence_weights[relationship.confidence.value]
            total_items += 1
        
        return (total_score / total_items) if total_items > 0 else 0.0
    
    def get_domain_insights(self, results: List[ProcessingResult] = None) -> Dict[str, Any]:
        """
        Analyze processing results to provide insights about discovered domains and patterns
        
        Args:
            results: Optional list of ProcessingResults to analyze. If None, uses processing_history
            
        Returns:
            Dictionary containing domain insights
        """
        if results is None:
            results = self.processing_history
        
        if not results:
            return {"message": "No processing results available"}
        
        # Aggregate statistics
        total_entities = sum(len(result.entities) for result in results)
        total_relationships = sum(len(result.relationships) for result in results)
        
        # Find most common entity types
        entity_type_counts = {}
        relationship_type_counts = {}
        
        for result in results:
            for entity in result.entities:
                entity_type_counts[entity.entity_type] = entity_type_counts.get(entity.entity_type, 0) + 1
            
            for relationship in result.relationships:
                relationship_type_counts[relationship.relationship_type] = relationship_type_counts.get(relationship.relationship_type, 0) + 1
        
        # Calculate average confidence
        all_confidences = []
        for result in results:
            all_confidences.extend([entity.confidence.value for entity in result.entities])
            all_confidences.extend([rel.confidence.value for rel in result.relationships])
        
        confidence_distribution = {"high": 0, "medium": 0, "low": 0}
        for conf in all_confidences:
            confidence_distribution[conf] += 1
        
        return {
            "total_conversations_processed": len(results),
            "total_entities_extracted": total_entities,
            "total_relationships_extracted": total_relationships,
            "most_common_entity_types": sorted(entity_type_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "most_common_relationship_types": sorted(relationship_type_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "confidence_distribution": confidence_distribution,
            "average_entities_per_conversation": total_entities / len(results) if results else 0,
            "average_relationships_per_conversation": total_relationships / len(results) if results else 0
        }
